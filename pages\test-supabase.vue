<template>
  <div class="p-8">
    <h1 class="text-2xl font-bold mb-4">Supabase Connection Test</h1>

    <div class="space-y-4">
      <button @click="testConnection" class="bg-blue-500 text-white px-4 py-2 rounded">
        Test Connection
      </button>

      <div v-if="loading" class="text-blue-600">
        Testing connection...
      </div>

      <div v-if="result" class="p-4 bg-green-100 rounded">
        <h3 class="font-bold text-green-800">Success!</h3>
        <pre class="text-sm">{{ JSON.stringify(result, null, 2) }}</pre>
      </div>

      <div v-if="error" class="p-4 bg-red-100 rounded">
        <h3 class="font-bold text-red-800">Error:</h3>
        <pre class="text-sm">{{ JSON.stringify(error, null, 2) }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const client = useSupabaseClient()
const user = useSupabaseUser()

const loading = ref(false)
const result = ref<any>(null)
const error = ref<any>(null)

const testConnection = async () => {
  loading.value = true
  result.value = null
  error.value = null

  try {
    console.log('Testing Supabase connection...')
    console.log('User:', user.value)

    // Test 1: Simple query without user filter
    const { data, error: queryError } = await client
      .from('academic_calendar_documents')
      .select('count', { count: 'exact', head: true })

    if (queryError) {
      throw queryError
    }

    result.value = {
      message: 'Connection successful!',
      user: user.value?.id || 'Not authenticated',
      count: data
    }

  } catch (e) {
    console.error('Supabase test error:', e)
    error.value = e
  } finally {
    loading.value = false
  }
}
</script>
