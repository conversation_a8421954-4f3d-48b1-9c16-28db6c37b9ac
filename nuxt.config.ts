import { defineNuxtConfig } from "nuxt/config";

// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: "2025-05-15",
  css: ["~/assets/css/tailwind.css"], // Removed effects.css as it's imported in tailwind.css
  modules: [
    "@pinia/nuxt",
    "@nuxtjs/tailwindcss",
    "@nuxtjs/supabase",
    [
      "@nuxtjs/google-fonts",
      {
        families: {
          Poppins: [400, 500, 600, 700],
        },
        preload: true,
        display: "swap",
      },
    ],
    "@nuxtjs/color-mode",
    "@nuxt/icon", // Updated from nuxt-icon to @nuxt/icon
  ],
  supabase: {
    url: process.env.SUPABASE_URL,
    key: process.env.SUPABASE_KEY,
    redirectOptions: {
      login: "/auth/login",
      callback: "/auth/confirm",
      exclude: [
        "/",
        "/auth/login",
        "/auth/daftar",
        "/auth/confirm",
        "/auth/butir-asas",
      ],
    },
    cookieOptions: {
      maxAge: 60 * 60 * 8, // 8 hours
      sameSite: 'lax',
      secure: true
    },
  },
  typescript: {
    strict: true,
    typeCheck: true, // Enable type checking during development and build
  },
  devtools: { enabled: true },
  colorMode: {
    preference: "system", // default value of $colorMode.preference
    fallback: "light", // fallback value if not system preference found
    classSuffix: "",
  },
  routeRules: {
    "/login": { redirect: "/auth/login" },
  },
});
